import React from 'react';
import { Shield, Lock, RefreshCw, Share2, Smartphone, Cloud, Code, Users, BarChart3, Settings, CheckCircle, AlertCircle } from 'lucide-react';

const FeaturesPage: React.FC = () => {
  const coreFeatures = [
    {
      icon: Lock,
      title: 'Zero-Knowledge Encryption',
      description: 'Your OTP secrets are encrypted client-side with AES-256. Your secondary password never leaves your device, ensuring complete privacy.',
      status: 'available'
    },
    {
      icon: RefreshCw,
      title: 'Multi-Device Sync',
      description: 'Access your OTP codes from any device. Encrypted data syncs securely across all your devices in real-time.',
      status: 'available'
    },
    {
      icon: Shield,
      title: 'Backup & Recovery',
      description: 'Generate secure recovery keys to protect against data loss. Export encrypted backups for additional security.',
      status: 'available'
    },
    {
      icon: Code,
      title: 'Developer API',
      description: 'REST API for automation and integration. Perfect for DevOps teams and custom workflows.',
      status: 'available'
    },
    {
      icon: Share2,
      title: 'Team Sharing',
      description: 'Securely share OTP access with team members. Role-based permissions and audit trails for compliance.',
      status: 'development'
    },
    {
      icon: Users,
      title: 'SSO Integration',
      description: 'Single sign-on with SAML, OIDC, and popular identity providers for enterprise environments.',
      status: 'planned'
    }
  ];

  const enterpriseFeatures = [
    {
      icon: BarChart3,
      title: 'Advanced Analytics',
      description: 'Usage analytics, security insights, and compliance reporting for enterprise teams.',
      status: 'development'
    },
    {
      icon: Settings,
      title: 'Admin Controls',
      description: 'Centralized user management, policy enforcement, and organizational settings.',
      status: 'development'
    },
    {
      icon: Cloud,
      title: 'On-Premise Deployment',
      description: 'Deploy Cloud OTP in your own infrastructure for maximum control and compliance.',
      status: 'planned'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="w-4 h-4 text-success" />;
      case 'development':
        return <AlertCircle className="w-4 h-4 text-warning" />;
      case 'planned':
        return <div className="w-4 h-4 rounded-full border-2 border-muted"></div>;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'development':
        return 'In Development';
      case 'planned':
        return 'Planned';
      default:
        return '';
    }
  };

  return (
    <>
      {/* Hero Section */}
      <section className="section bg-slate-50">
        <div className="container text-center">
          <h1 className="text-4xl font-bold text-primary mb-6">
            Professional OTP Management Features
          </h1>
          <p className="text-xl text-secondary max-w-3xl mx-auto mb-8">
            Everything you need for secure, scalable two-factor authentication management. 
            Built for individuals, teams, and enterprises.
          </p>
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
            <span>ℹ️</span>
            <span>Feature status is updated in real-time - no marketing fluff</span>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section className="section">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">Core Features</h2>
            <p className="text-lg text-secondary max-w-3xl mx-auto">
              Essential OTP management capabilities available to all users.
            </p>
          </div>

          <div className="card-grid">
            {coreFeatures.map((feature, index) => (
              <div key={index} className="card hover-lift">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-blue-100 text-blue-600">
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(feature.status)}
                    <span className="text-xs font-medium text-muted">
                      {getStatusText(feature.status)}
                    </span>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-primary mb-3">
                  {feature.title}
                </h3>
                <p className="text-secondary">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enterprise Features */}
      <section className="section bg-slate-50">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">Enterprise Features</h2>
            <p className="text-lg text-secondary max-w-3xl mx-auto">
              Advanced capabilities for organizations and teams.
            </p>
          </div>

          <div className="card-grid">
            {enterpriseFeatures.map((feature, index) => (
              <div key={index} className="card hover-lift">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-purple-100 text-purple-600">
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(feature.status)}
                    <span className="text-xs font-medium text-muted">
                      {getStatusText(feature.status)}
                    </span>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-primary mb-3">
                  {feature.title}
                </h3>
                <p className="text-secondary">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Security Section */}
      <section className="section bg-primary text-inverse">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Security First</h2>
            <p className="text-lg max-w-3xl mx-auto opacity-90">
              Built with enterprise-grade security from the ground up.
            </p>
          </div>

          <div className="card-grid max-w-4xl mx-auto">
            <div className="card bg-white/10 border-white/20 text-inverse">
              <h3 className="text-xl font-semibold mb-3">Client-Side Encryption</h3>
              <p className="opacity-90">
                All encryption happens on your device. We never see your OTP secrets in plain text.
              </p>
            </div>

            <div className="card bg-white/10 border-white/20 text-inverse">
              <h3 className="text-xl font-semibold mb-3">Zero-Knowledge Architecture</h3>
              <p className="opacity-90">
                Even if our servers are compromised, your data remains secure and inaccessible.
              </p>
            </div>

            <div className="card bg-white/10 border-white/20 text-inverse">
              <h3 className="text-xl font-semibold mb-3">Open Source Security</h3>
              <p className="opacity-90">
                Our encryption implementation is open source and auditable by security experts.
              </p>
            </div>

            <div className="card bg-white/10 border-white/20 text-inverse">
              <h3 className="text-xl font-semibold mb-3">No Recovery Backdoors</h3>
              <p className="opacity-90">
                If you lose your secondary password, your data is gone forever. This is by design.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section">
        <div className="container text-center">
          <h2 className="text-3xl font-bold text-primary mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-lg text-secondary mb-8 max-w-2xl mx-auto">
            Experience professional OTP management with enterprise-grade security.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/auth" className="btn btn-primary btn-lg">
              Start Free
            </a>
            <a href="/pricing" className="btn btn-secondary btn-lg">
              View Pricing
            </a>
          </div>
        </div>
      </section>
    </>
  );
};

export default FeaturesPage;
