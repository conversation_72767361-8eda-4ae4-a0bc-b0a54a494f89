import React, { useState } from 'react';
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  Send,
  CheckCircle,
  Building2,
  Users,
  MessageSquare,
  Headphones,
  Globe,
  Shield
} from 'lucide-react';
import { useToast } from '../../contexts/ToastContext';

const ContactPage: React.FC = () => {
  const { showToast } = useToast();
  const [formStatus, setFormStatus] = useState<'idle' | 'submitting' | 'success' | 'error'>('idle');

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setFormStatus('submitting');

    // Simulate form submission
    setTimeout(() => {
      setFormStatus('success');
      showToast('Message sent successfully! We\'ll get back to you within 24 hours.', 'success');

      // Reset form after 3 seconds
      setTimeout(() => {
        setFormStatus('idle');
        (e.target as HTMLFormElement).reset();
      }, 3000);
    }, 1500);
  };

  return (
    <>
      {/* Hero Section */}
      <section className="section-lg bg-gradient-to-br from-slate-900 to-blue-900 text-inverse">
        <div className="container text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl font-bold mb-6">
              Get in Touch with Our Team
            </h1>
            <p className="text-xl mb-8 opacity-90 leading-relaxed">
              Whether you're evaluating Cloud OTP for your organization, need technical support,
              or want to discuss enterprise requirements, we're here to help.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div className="flex items-center justify-center gap-2">
                <Clock className="w-4 h-4" />
                <span>24-hour response time</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <Shield className="w-4 h-4" />
                <span>Enterprise security experts</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <Globe className="w-4 h-4" />
                <span>Global support coverage</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Options */}
      <section className="section">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-primary mb-6">
              Choose How You'd Like to Connect
            </h2>
            <p className="text-xl text-secondary max-w-3xl mx-auto">
              We offer multiple ways to get in touch based on your needs and urgency level.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 mb-16">
            <div className="card text-center hover-lift">
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-blue-100 text-blue-600 mx-auto mb-4">
                <Building2 className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-2">Enterprise Sales</h3>
              <p className="text-secondary mb-4">
                Discuss enterprise requirements, custom pricing, and deployment options.
              </p>
              <a href="mailto:<EMAIL>" className="btn btn-primary">
                Contact Sales
              </a>
            </div>

            <div className="card text-center hover-lift">
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-emerald-100 text-emerald-600 mx-auto mb-4">
                <Headphones className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-2">Technical Support</h3>
              <p className="text-secondary mb-4">
                Get help with technical issues, integration questions, and platform usage.
              </p>
              <a href="mailto:<EMAIL>" className="btn btn-secondary">
                Get Support
              </a>
            </div>

            <div className="card text-center hover-lift">
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-purple-100 text-purple-600 mx-auto mb-4">
                <Users className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-2">Partnership</h3>
              <p className="text-secondary mb-4">
                Explore partnership opportunities, integrations, and business development.
              </p>
              <a href="mailto:<EMAIL>" className="btn btn-secondary">
                Partner With Us
              </a>
            </div>

            <div className="card text-center hover-lift">
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-amber-100 text-amber-600 mx-auto mb-4">
                <MessageSquare className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-2">General Inquiry</h3>
              <p className="text-secondary mb-4">
                Questions about our platform, pricing, or anything else we can help with.
              </p>
              <a href="mailto:<EMAIL>" className="btn btn-secondary">
                Send Message
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="section bg-slate-50">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-primary mb-4">
                Send Us a Detailed Message
              </h2>
              <p className="text-lg text-secondary">
                Prefer to send a detailed message? Use the form below and we'll get back to you within 24 hours.
              </p>
            </div>

            <div className="card">
              {formStatus === 'success' ? (
                <div className="text-center py-12">
                  <div className="flex items-center justify-center w-16 h-16 rounded-full bg-success/10 text-success mx-auto mb-4">
                    <CheckCircle className="w-8 h-8" />
                  </div>
                  <h3 className="text-2xl font-semibold text-primary mb-2">Message Sent Successfully!</h3>
                  <p className="text-secondary">
                    Thank you for contacting us. We'll review your message and get back to you within 24 hours.
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="form-group">
                      <label htmlFor="firstName" className="form-label required">
                        First Name
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        required
                        className="form-input"
                        placeholder="Enter your first name"
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="lastName" className="form-label required">
                        Last Name
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        required
                        className="form-input"
                        placeholder="Enter your last name"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="form-group">
                      <label htmlFor="email" className="form-label required">
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        className="form-input"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="company" className="form-label">
                        Company
                      </label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        className="form-input"
                        placeholder="Your company name"
                      />
                    </div>
                  </div>

                  <div className="form-group mb-6">
                    <label htmlFor="subject" className="form-label required">
                      Subject
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      required
                      className="form-input form-select"
                    >
                      <option value="">Select a subject</option>
                      <option value="enterprise">Enterprise Sales Inquiry</option>
                      <option value="support">Technical Support</option>
                      <option value="partnership">Partnership Opportunity</option>
                      <option value="billing">Billing Question</option>
                      <option value="security">Security Question</option>
                      <option value="feature">Feature Request</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div className="form-group mb-6">
                    <label htmlFor="message" className="form-label required">
                      Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      required
                      rows={6}
                      className="form-input"
                      placeholder="Please provide details about your inquiry, including any specific requirements or questions you have..."
                    ></textarea>
                  </div>

                  <div className="flex items-start gap-3 mb-6">
                    <input
                      type="checkbox"
                      id="privacy"
                      name="privacy"
                      required
                      className="form-checkbox mt-1"
                    />
                    <label htmlFor="privacy" className="text-sm text-secondary">
                      I agree to the <a href="/privacy" className="text-accent hover:text-accent-hover">Privacy Policy</a> and
                      consent to Cloud OTP storing and processing my personal information to respond to this inquiry.
                    </label>
                  </div>

                  <button
                    type="submit"
                    disabled={formStatus === 'submitting'}
                    className="btn btn-primary btn-lg w-full"
                  >
                    {formStatus === 'submitting' ? (
                      <span className="inline-flex items-center">
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                        Sending Message...
                      </span>
                    ) : (
                      <span className="inline-flex items-center">
                        <Send className="w-4 h-4 mr-2" />
                        Send Message
                      </span>
                    )}
                  </button>
                </form>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Support Information */}
      <section className="section">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-primary mb-4">
                Support & Response Times
              </h2>
              <p className="text-lg text-secondary">
                We're committed to providing excellent support across all our communication channels.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2">
              <div className="card">
                <h3 className="text-xl font-semibold text-primary mb-4">
                  <Clock className="w-5 h-5 inline mr-2" />
                  Support Hours
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-secondary">Monday - Friday</span>
                    <span className="font-medium text-primary">9:00 AM - 6:00 PM EST</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary">Saturday - Sunday</span>
                    <span className="font-medium text-primary">Emergency support only</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary">Enterprise customers</span>
                    <span className="font-medium text-primary">24/7 support available</span>
                  </div>
                </div>
              </div>

              <div className="card">
                <h3 className="text-xl font-semibold text-primary mb-4">
                  <MessageSquare className="w-5 h-5 inline mr-2" />
                  Response Times
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-secondary">General inquiries</span>
                    <span className="font-medium text-primary">Within 24 hours</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary">Technical support</span>
                    <span className="font-medium text-primary">Within 12 hours</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary">Enterprise customers</span>
                    <span className="font-medium text-primary">Within 4 hours</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-12 grid gap-6 md:grid-cols-3">
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-blue-100 text-blue-600 mx-auto mb-3">
                  <Mail className="w-6 h-6" />
                </div>
                <h4 className="font-semibold text-primary mb-1">Email Support</h4>
                <p className="text-sm text-secondary mb-2">For detailed inquiries and documentation</p>
                <a href="mailto:<EMAIL>" className="text-accent hover:text-accent-hover text-sm">
                  <EMAIL>
                </a>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-emerald-100 text-emerald-600 mx-auto mb-3">
                  <Building2 className="w-6 h-6" />
                </div>
                <h4 className="font-semibold text-primary mb-1">Enterprise Sales</h4>
                <p className="text-sm text-secondary mb-2">Custom solutions and enterprise pricing</p>
                <a href="mailto:<EMAIL>" className="text-accent hover:text-accent-hover text-sm">
                  <EMAIL>
                </a>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-purple-100 text-purple-600 mx-auto mb-3">
                  <Shield className="w-6 h-6" />
                </div>
                <h4 className="font-semibold text-primary mb-1">Security Issues</h4>
                <p className="text-sm text-secondary mb-2">Report security vulnerabilities</p>
                <a href="mailto:<EMAIL>" className="text-accent hover:text-accent-hover text-sm">
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section bg-slate-50">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-secondary max-w-3xl mx-auto">
              Quick answers to common questions. Can't find what you're looking for? Contact us directly.
            </p>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                How quickly can I get started with Cloud OTP?
              </h3>
              <p className="text-secondary">
                You can start using Cloud OTP immediately with our free tier. Simply create an account,
                set up your secondary password, and begin adding your OTP secrets. The entire process takes less than 5 minutes.
              </p>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                Do you offer enterprise trials?
              </h3>
              <p className="text-secondary">
                Yes, we offer 30-day enterprise trials with full access to all features. Contact our sales team
                to set up a trial environment for your organization.
              </p>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                What security certifications do you have?
              </h3>
              <p className="text-secondary">
                We're currently pursuing SOC 2 Type II certification and are GDPR/CCPA compliant.
                Our zero-knowledge architecture ensures your data remains secure regardless of certifications.
              </p>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                Can you help with migration from other OTP solutions?
              </h3>
              <p className="text-secondary">
                Absolutely! We provide migration assistance for enterprise customers, including data export/import
                tools and dedicated support during the transition process.
              </p>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                Do you offer on-premise deployment?
              </h3>
              <p className="text-secondary">
                On-premise deployment is planned for Q2 2025. Enterprise customers can join our early access program
                to influence the development and be among the first to deploy.
              </p>
            </div>
          </div>

          <div className="text-center mt-12">
            <a
              href="/help"
              className="inline-flex items-center text-accent hover:text-accent-hover font-medium"
            >
              View complete help documentation
              <svg className="ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </a>
          </div>
        </div>
      </section>
    </>
  );
};

export default ContactPage;