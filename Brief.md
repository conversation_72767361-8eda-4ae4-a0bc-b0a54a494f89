# Cloud OTP — Platform Brief

**Author:** <PERSON>

**Date Edit:** August 17, 2025 
**Email:** <EMAIL> 
**Author URL:** [skpassegna.me](https://skpassegna.me/) 
**Platform:** [Online Demo Version](https://otp-cloud-gamma.vercel.app/) 

__Exploiting company: Leaf SARL-U, Lome Togo.__

> No open source code, all code are closed source and will be disclosed ONLY to relevant authority for security and complaince audit.
> The implementrations and considerations MUST follow world class and commercial grade production ready coding and architecture!
> No unit test is needed for now, focus only on coding a working platform.
> Strictly adhere to UI-first and SOLID principles!
---

## Project Overview

Cloud OTP is a secure, cloud-based platform for managing TOTP/HOTP secrets, designed to eliminate reliance on physical devices for one-time password (OTP) management. The platform supports individual, team, and enterprise use cases, with robust admin controls, advanced sharing, and deep integration capabilities. It is designed for extensibility, security, and seamless user experience across all account types.

## Platform Roles & Account Types

### 1. Super Admin & Developer Admin Dashboards

- **Super Admin**: Full platform control, including user, team, sales, and marketing account management, system settings, and audit logs.
    - Edit platform name, branding, and company information
    - Define and manage pricing structures (single pricing, multiple pricing groups, or contact sales-based pricing)
    - Configure feature availability within each pricing tier
    - Edit and activate legal pages (Terms of Service, Privacy Policy, Cookies Policy, etc.)
    - Manage contact information and page details
    - Edit help content and documentation
- **Developer Admin**: Manages technical integrations, API access, developer accounts, and platform resource allocation.
    - Configure platform URLs and domains
    - Edit technical documentation and API references
    - Manage developer resources and help content
- **Dashboards**:
    - Real-time analytics (usage, security events, credit balances, API activity)
    - User/team management (CRUD, status, permissions)
    - Resource management (API quotas, storage, rate limits)
    - System health, logs, and alerts
    - Coupon, invitation, and credit management
    - Payment and billing overview

### 2. Sales Accounts (Platform Admin)

- Manage and track sales-driven user and team accounts
- Create and distribute coupon codes
- Assign credits to accounts
- View sales analytics and conversion metrics
- Collaborate with marketing/admins
- Edit sales-related help content and documentation

### 3. Marketing Accounts (Platform Admin)

- Manage marketing campaigns and referral programs
- Generate and distribute invitation codes
- Track campaign performance and coupon usage
- Collaborate with sales/admins
- Edit marketing-related help content

## Account Management & Access

### Account Credit Balances

- Each account has a credit balance (used for API calls, premium features, etc.)
- Super Admin and Sales Admin can add credits to any account
- Users can purchase credits via integrated payment processing
- Credit usage is tracked and visible in dashboards
- Credit allocations are tied to pricing tiers defined by Super Admin

### Admin-Driven Account Creation & Resource Management

- Admins can create accounts (user, team, partner) directly
- Set initial quotas, credit, and resource limits
- Assign roles and permissions
- Suspend, reactivate, or delete accounts
- Audit trail for all admin actions
- Assign accounts to specific pricing tiers

### Invitation-Based Account Signup

- Accounts can be created via invitation (referral, team member, partner, or admin-generated)
- Invitation codes can be single-use or multi-use, with optional expiration
- Admins can track invitation status and usage
- Invitations can be pre-assigned to specific pricing tiers

### Coupon-Code Support

- Super Admin and Sales Admin can create, distribute, and manage coupon codes
- Coupons can provide credit, discounts, or premium access
- Coupon usage is tracked and auditable
- Coupons can be tied to specific pricing tiers or features

### Payment Processing (Paddle.com)

- Integrated with Paddle for payment processing
- Supports credit purchases, subscription management, and invoicing
- Handles VAT/tax, refunds, and compliance
- Payment events are logged and auditable
- Pricing tier selection is integrated into checkout flow

## Team & User Features

### Client REST API Access

- All account types (team, individual) can access a REST API for managing their OTPs, automating workflows, and integrating with other systems
- API keys are managed by accounts' Admins
- API endpoints for OTP CRUD, sharing, folder management, notes, team management
- API rate limits and quotas are enforced per account according to pricing tier
- API access is a core platform feature, not just for platform administration

### Team Accounts & Management

- Team accounts can invite/manage members (role-based access)
- Team owners/admins can assign roles (admin, member, read-only, etc.)
- Team-based access controls for OTPs, folders, and notes
- Team activity logs and audit trails
- Team size and feature access is determined by pricing tier

### Single Sign-On (SSO) & Social Logins

- SSO support (SAML, OIDC, or similar)
- Social login support: Facebook, GitHub, Google, Microsoft, LinkedIn (preferred)
- Users can link/unlink social accounts
- SSO and social login events are logged

### OTP Sharing & Collaboration

- OTPs can be shared with other users or teams, either temporarily or permanently
- Sharing via secure URL (with or without password protection, expiration, and access limits, this function is available for any kind of user and a special sharing with non-users with a non-auth unique page - unique sharing URL - included too)
- Shared OTPs can be revoked at any time
- Audit logs for all sharing actions (Pro, and enterprise account)
- Advanced sharing features may be tier-restricted

### OTP Grouping & Folder Management (Any account type)

- OTPs can be organized into folders (which can be shared)
- Folders support access controls (team, user, public link)
- Folders can be nested or tagged for organization
- Folder quantity and depth may vary by pricing tier

### Team-Based Access Management

- Fine-grained access controls for OTPs, folders, and notes
- Role-based permissions (view, edit, share, delete)
- Access can be granted/revoked by team admins or platform admins
- Advanced permission features may be tier-restricted

### Notes on OTP Entries

- Users can attach notes to OTP entries (e.g., usage instructions, context)
- Notes are encrypted and access-controlled
- Notes can be shared along with OTPs or folders
- Note length and features may vary by pricing tier

---

## Platform Configuration

### Branding & Customization

- Super Admin can edit platform name, logo, and company information
- Custom CSS and theme options for enterprise customers
- White-label options for select partner integrations
- Email template customization for system notifications

### Legal & Compliance Management

- Super Admin can edit and activate all legal pages:
    - Terms of Service
    - Privacy Policy
    - Cookie Policy
    - Data Processing Agreements
    - Service Level Agreements
    - Acceptable Use Policy
- Version history of all legal documents is maintained
- Users are prompted to accept updated terms when necessary
- Legal page activation/deactivation with audit logging

### Contact & Support Information

- Super Admin can edit all contact information:
    - Support email addresses
    - Physical addresses
    - Phone numbers
    - Support hours
    - Contact forms and routing rules
- Contact information is consistently displayed across the platform

### Help Content Management

- Role-based content editing:
    - Super Admin: All content
    - Developer Admin: Technical documentation, API references
    - Sales Admin: Pricing, billing, and account management guides
    - Marketing Admin: Feature guides and promotional materials
- Content version history and publishing workflow
- Multi-language support for help content
- Searchable knowledge base with analytics

### URL & Domain Configuration

- Developer Admin can configure:
    - Primary domain and subdomains
    - API endpoints and versioning
    - Custom domains for enterprise clients
    - Redirect rules and URL structures
- URL changes are audited and require approval
- SSL certificate management

### Pricing & Feature Management

- Super Admin can define multiple pricing models:
    - Single flat pricing for all users
    - Tiered pricing groups (e.g., Free, Pro, Enterprise)
    - Custom/contact sales pricing for large enterprises
- Feature availability matrix per pricing tier
- Granular control over feature limits (e.g., number of OTPs, API calls, team members)
- Special pricing for non-profits, educational institutions
- Promotional pricing and time-limited offers

## Support & Ticketing

### Built-In Support/Ticketing System

- Users and teams can submit support tickets directly from the platform
- Tickets are routed to Admins (Super, Sales, Developer)
- Admins can respond, escalate, and resolve tickets
- Ticket status, history, and communication are tracked
- Support analytics and satisfaction metrics available to Admins
- Support levels vary by pricing tier (basic, standard, premium)

## Security & Compliance

- All sensitive data (OTPs, notes, user info) is encrypted at rest and in transit
- Zero-knowledge encryption for OTP secrets (only user/team can decrypt)
- Full audit logging for all critical actions (admin, user, API)
- Role-based access control (RBAC) throughout
- GDPR, and CCPA compliance support
- Dynamic session timeouts, 2FA for admin access
- Secure password and recovery key management (not saved in database)

### Secondary Password & Recovery Key

Cloud OTP implements a robust, zero-knowledge encryption model to ensure that only the user (or authorized team) can ever access their OTP secrets. This is achieved through a combination of a secondary password (encryption password) and a user-held recovery key (backup code):

### Secondary Password (Encryption Password)

- **Purpose:** Used exclusively to encrypt and decrypt all OTP secrets and sensitive data. Never sent to the server or stored in any form.
- **Setup:**
    - Users must create a strong secondary password on first use, meeting strict security requirements (length, character types, entropy, no repeats/sequences).
    - A random data encryption key and salt are generated client-side.
    - The secondary password and salt are used to derive a master key (PBKDF2 or similar KDF).
    - The data encryption key is encrypted with the master key and stored (encrypted) in the database.
- **Unlocking:**
    - To access encrypted OTPs, the user must enter their secondary password.
    - The app derives the master key, decrypts the data key, and loads it into memory for the session only.
    - If the password is incorrect, decryption fails and access is denied.
- **Session Management:**
    - The decrypted data key is only kept in memory for a limited session (default: 15 minutes, randomized).
    - The session can be extended or locked manually; when expired or locked, the key is wiped from memory.
- **Password Reset:**
    - Users can reset their secondary password if they know the current one. The data key is re-encrypted with the new password-derived key and salt.
    - At no point is the password or any decryptable form of it stored or transmitted.

### Recovery Key (Backup Code)

- **Purpose:** A backup mechanism in case the user forgets their secondary password. Never stored on the server or in the database.
- **Generation & Format:**
    - Generated as a 32-byte random value, formatted for readability (e.g., XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX).
    - Shown to the user only once during setup, with strong prompts to save it securely (copy, download, print).
- **Usage:**
    - The data encryption key is also encrypted with the recovery key and stored in the database.
    - If the user forgets their secondary password, they can use the recovery key to decrypt the data key and set a new password.
    - If both the secondary password and recovery key are lost, **data is permanently unrecoverable**.
- **User Guidance:**
    - The UI requires users to confirm they have saved the recovery key before proceeding.
    - Users are warned never to share the recovery key and to store it in a secure location (password manager, safe, etc.).

### Data Recovery & Account Lockout Policy

- **No Recovery:** The platform is built on the principle that user data is unrecoverable if the secondary password is lost. There is **no backup code or recovery key mechanism**. This design choice is fundamental to the zero-knowledge security model.
- **Lost Password:** If a user forgets their secondary password, their encrypted data will be permanently inaccessible. Platform administrators have no technical means to reset the password or decrypt the user's data. All appeals will be denied, as access is cryptographically impossible.
- **Account Lockout on Failed Attempts:** To protect against brute-force attacks, a strict lockout policy is enforced on the secondary password:
    - **5 incorrect attempts:** The account is locked for **24 hours**. An email notification is sent to the user.
    - **6th incorrect attempt:** The account is locked for **48 hours**.
    - **7th incorrect attempt:** The account is locked for **72 hours**.
    - **8th incorrect attempt:** The account is locked for **1 week**.
    - **9th incorrect attempt:** The account is locked for **1 month**.
    - **10th incorrect attempt:** The account is locked for **1 year**.
    - **11th incorrect attempt:** The account is **permanently locked**. The data remains encrypted and inaccessible forever.

### Cryptographic Principles

- **All cryptographic operations** are performed client-side using strong, industry-standard algorithms.
- **Zero-knowledge:** Neither the secondary password nor the recovery key is ever sent to the server.
- **Database** only stores encrypted keys, IVs, and salts—never secrets or passwords in plaintext.
- **Password validation** is enforced at setup/reset, including entropy and character requirements.
- **Recovery flow** allows the user to enter the recovery key and set a new secondary password, re-encrypting the data key.

### Further Improvements

- **Shamir's Secret Sharing:** Allow users to split the recovery key into multiple parts, requiring a threshold to recover (for advanced users/teams).
- **Automated Encrypted Backup:** Allow users to export an encrypted backup of their OTP vault, decryptable only with their secondary password or recovery key.
- **Periodic Reminders:** Remind users to verify their recovery key is still accessible.

**Summary:**
Cloud OTP's encryption model is designed to be foolproof: only the user can ever decrypt their data, and no one (not even platform admins) can recover data without the user's credentials. This approach maximizes both security and user control, while providing a clear, user-friendly recovery path that is robust against both technical and human error.

## Extensibility & Integration

- REST API for all major features (OTP, folders, sharing, notes, teams, support)
- Webhooks for key events (sharing, payment, support, etc.)
- Modular design for future feature expansion (e.g., browser extension, mobile & desktop app)
- API access levels and rate limits vary by pricing tier

## Pricing

### Free

**$0**

Perfect for personal use

- Unlimited OTP storage
- Multi-device sync
- Basic API access
- Email support
[Start Free](http://localhost:5173/auth)

Most Popular

### Pro

**$1 per month**

For power users and small teams

- Everything in Free
- Priority support
- Advanced API features
- Export capabilities
[Start Pro Trial](http://localhost:5173/auth)

### Enterprise

**Single 2$ for pro features + $5 per user/month**

For teams and organizations

- Free up to 5 users
- Team collaboration
- Admin dashboard
- 24/7 support

## Usage Scenarios

- **Enterprise**: Centralized OTP management for teams, SSO, audit, and compliance
- **SMB/Startup**: Team-based OTP sharing, folder management, and support
- **Individual**: Secure, phone-free OTP management, sharing, and backup
- **Partners/Resellers**: Admin-driven account creation, credit/coupon management, and analytics
- **Developers**: API integration with existing systems and workflows

## Non-Goals

- Cloud OTP does not store or manage user passwords for third-party services
- Does not provide SMS/voice OTP delivery
- Does not act as a general password manager (focus is on OTP/TOTP/HOTP)

## Future Roadmap

- Browser extension for autofill and quick access
- Mobile & Desktop app for offline/backup OTP access
- Advanced analytics and reporting
- Integration with additional payment providers
- Custom branding/white-label support
- Multi-regional data centers for compliance and latency optimization
- Advanced automation and workflow integrations

---

*This brief is exhaustive and designed to guide a full-featured, secure, and scalable implementation of Cloud OTP, agnostic of any specific technology stack.*